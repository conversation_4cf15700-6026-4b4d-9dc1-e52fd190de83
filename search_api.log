2025-07-21 15:26:06,842 - __main__ - INFO - Starting up Search API...
2025-07-21 15:26:06,842 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-21 15:26:06,846 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-21 15:26:06,846 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-21 15:26:10,805 - __main__ - INFO - Model loaded successfully
2025-07-21 15:26:10,806 - __main__ - INFO - Initializing database connection...
2025-07-21 15:26:10,918 - __main__ - INFO - Database connection established
2025-07-21 15:26:10,920 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-21 15:26:10,920 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-07-21 15:26:11,035 - __main__ - INFO - Total records to process: 75513
2025-07-21 15:26:11,037 - __main__ - INFO - Processing chunk 1: records 0-5000
2025-07-21 15:26:15,962 - __main__ - INFO - Progress: 5000/75513 records processed (6.6%)
2025-07-21 15:26:15,963 - __main__ - INFO - Processing chunk 2: records 5000-10000
2025-07-21 15:26:20,929 - __main__ - INFO - Processing chunk 3: records 10000-15000
2025-07-21 15:26:25,432 - __main__ - INFO - Processing chunk 4: records 15000-20000
2025-07-21 15:26:29,656 - __main__ - INFO - Processing chunk 5: records 20000-25000
2025-07-21 15:26:33,565 - __main__ - INFO - Processing chunk 6: records 25000-30000
2025-07-21 15:26:38,131 - __main__ - INFO - Progress: 30000/75513 records processed (39.7%)
2025-07-21 15:26:38,131 - __main__ - INFO - Processing chunk 7: records 30000-35000
2025-07-21 15:26:42,510 - __main__ - INFO - Processing chunk 8: records 35000-40000
2025-07-21 15:26:46,909 - __main__ - INFO - Processing chunk 9: records 40000-45000
2025-07-21 15:26:51,320 - __main__ - INFO - Processing chunk 10: records 45000-50000
2025-07-21 15:26:56,336 - __main__ - INFO - Processing chunk 11: records 50000-55000
2025-07-21 15:27:00,847 - __main__ - INFO - Progress: 55000/75513 records processed (72.8%)
2025-07-21 15:27:00,847 - __main__ - INFO - Processing chunk 12: records 55000-60000
2025-07-21 15:27:05,467 - __main__ - INFO - Processing chunk 13: records 60000-65000
2025-07-21 15:27:09,824 - __main__ - INFO - Processing chunk 14: records 65000-70000
2025-07-21 15:27:14,758 - __main__ - INFO - Processing chunk 15: records 70000-75000
2025-07-21 15:27:20,818 - __main__ - INFO - Processing chunk 16: records 75000-75513
2025-07-21 15:27:22,136 - __main__ - INFO - Progress: 75513/75513 records processed (100.0%)
2025-07-21 15:27:22,137 - __main__ - INFO - Saving BM25 index to disk: 160583 terms, 75513 documents
2025-07-21 15:27:23,472 - __main__ - INFO - BM25 index saved to disk
2025-07-21 15:27:23,650 - __main__ - INFO - GC: 0 objects collected, 8.8MB freed
2025-07-21 15:27:23,667 - __main__ - INFO - Data loading complete: 75513 records saved to search_cache
2025-07-21 15:27:23,733 - __main__ - INFO - Chunked cache loading completed in 72.81s
2025-07-21 15:27:23,734 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-21 15:27:23,909 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-21 15:27:23,910 - __main__ - INFO - Memory-mapped storage: True
2025-07-21 15:27:34,068 - __main__ - INFO - Request 1753126054-1429398277536: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:27:34,070 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-21 15:27:34,071 - __main__ - INFO - Processing search: 'theft' (Memory: 504.0MB)
2025-07-21 15:27:34,526 - __main__ - INFO - Search completed: 20 results in 455.36ms (Memory: 504.0MB -> 661.7MB, diff: +157.7MB)
2025-07-21 15:27:34,692 - __main__ - WARNING - Request 1753126054-1429398277536 increased memory by 157.8MB (from 504.0MB to 661.8MB)
2025-07-21 15:27:34,844 - __main__ - INFO - Request 1753126054-1429398277536 completed in 623.99ms with status 200
2025-07-21 15:28:00,816 - __main__ - INFO - Request 1753126080-1429402467840: GET http://localhost:8000/search?q=domestic%20violence&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:28:00,818 - __main__ - INFO - Processing search request: 'domestic violence' on table 'ReportNLP'
2025-07-21 15:28:00,818 - __main__ - INFO - Processing search: 'domestic violence' (Memory: 661.8MB)
2025-07-21 15:28:01,218 - __main__ - INFO - Search completed: 20 results in 400.23ms (Memory: 661.8MB -> 662.0MB, diff: +0.2MB)
2025-07-21 15:28:01,221 - __main__ - INFO - Request 1753126080-1429402467840 completed in 405.24ms with status 200
2025-07-21 15:28:52,169 - __main__ - INFO - Request 1753126132-1429405012992: GET http://localhost:8000/search?q=violence&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:28:52,170 - __main__ - INFO - Processing search request: 'violence' on table 'ReportNLP'
2025-07-21 15:28:52,170 - __main__ - INFO - Processing search: 'violence' (Memory: 662.1MB)
2025-07-21 15:28:52,509 - __main__ - INFO - Search completed: 20 results in 339.42ms (Memory: 662.1MB -> 662.2MB, diff: +0.1MB)
2025-07-21 15:28:52,512 - __main__ - INFO - Request 1753126132-1429405012992 completed in 342.98ms with status 200
2025-07-21 15:29:10,483 - __main__ - INFO - Request 1753126150-1429405011120: GET http://localhost:8000/search?q=fight&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:29:10,483 - __main__ - INFO - Processing search request: 'fight' on table 'ReportNLP'
2025-07-21 15:29:10,484 - __main__ - INFO - Processing search: 'fight' (Memory: 662.2MB)
2025-07-21 15:29:10,818 - __main__ - INFO - Search completed: 20 results in 333.48ms (Memory: 662.2MB -> 662.6MB, diff: +0.4MB)
2025-07-21 15:29:10,820 - __main__ - INFO - Request 1753126150-1429405011120 completed in 338.05ms with status 200
2025-07-21 15:30:19,658 - __main__ - INFO - Request 1753126219-1429414504512: GET http://localhost:8000/memory
2025-07-21 15:30:19,659 - __main__ - INFO - Request 1753126219-1429414504512 completed in 1.00ms with status 200
2025-07-22 08:17:25,557 - __main__ - INFO - Request 1753186645-1429405049936: GET http://localhost:8000/memory
2025-07-22 08:17:25,559 - __main__ - INFO - Request 1753186645-1429405049936 completed in 2.00ms with status 200
2025-07-22 08:17:39,562 - __main__ - INFO - Request 1753186659-1429399870336: GET http://localhost:8000/search?q=vandalism&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 08:17:39,563 - __main__ - INFO - Processing search request: 'vandalism' on table 'ReportNLP'
2025-07-22 08:17:39,564 - __main__ - INFO - Processing search: 'vandalism' (Memory: 662.8MB)
2025-07-22 08:17:40,305 - __main__ - INFO - Search completed: 20 results in 741.81ms (Memory: 662.8MB -> 662.9MB, diff: +0.2MB)
2025-07-22 08:17:40,307 - __main__ - INFO - Request 1753186659-1429399870336 completed in 745.53ms with status 200
2025-07-22 08:24:19,960 - __main__ - INFO - Request 1753187059-1429403374256: GET http://localhost:8000/memory
2025-07-22 08:24:19,983 - __main__ - INFO - Request 1753187059-1429403374256 completed in 22.99ms with status 200
2025-07-22 08:24:32,139 - __main__ - INFO - Request 1753187072-1429405041872: GET http://localhost:8000/search?q=car&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 08:24:32,179 - __main__ - INFO - Processing search request: 'car' on table 'ReportNLP'
2025-07-22 08:24:32,199 - __main__ - INFO - Processing search: 'car' (Memory: 663.0MB)
2025-07-22 08:24:34,074 - __main__ - INFO - Search completed: 20 results in 1873.56ms (Memory: 663.0MB -> 663.0MB, diff: +0.0MB)
2025-07-22 08:24:34,124 - __main__ - INFO - Request 1753187072-1429405041872 completed in 1984.60ms with status 200
2025-07-22 08:24:49,232 - __main__ - INFO - Request 1753187089-1429399671712: GET http://localhost:8000/memory
2025-07-22 08:24:49,260 - __main__ - INFO - Request 1753187089-1429399671712 completed in 28.00ms with status 200
2025-07-22 08:25:12,366 - __main__ - INFO - Shutting down Search API...
2025-07-22 08:25:12,449 - __main__ - INFO - Database connection closed
2025-07-22 08:55:38,535 - __main__ - INFO - Starting up Search API...
2025-07-22 08:55:38,536 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 08:55:38,543 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 08:55:38,543 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 08:55:42,241 - __main__ - INFO - Model loaded successfully
2025-07-22 08:55:42,241 - __main__ - INFO - Initializing database connection...
2025-07-22 08:55:42,415 - __main__ - INFO - Database connection established
2025-07-22 08:55:42,415 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 08:55:42,416 - __main__ - INFO - All cache files found on disk
2025-07-22 08:55:42,417 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 08:55:42,450 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 08:55:42,470 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 08:55:43,324 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 08:55:43,324 - __main__ - INFO - Cache loaded from disk in 0.91s
2025-07-22 08:55:43,325 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 08:55:43,644 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 08:55:43,645 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 08:59:13,191 - __main__ - INFO - Shutting down Search API...
2025-07-22 08:59:13,194 - __main__ - INFO - Database connection closed
2025-07-22 09:01:40,721 - __main__ - INFO - Starting up Search API...
2025-07-22 09:01:40,721 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:01:40,724 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:01:40,725 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:01:43,660 - __main__ - INFO - Model loaded successfully
2025-07-22 09:01:43,660 - __main__ - INFO - Initializing database connection...
2025-07-22 09:01:43,793 - __main__ - INFO - Database connection established
2025-07-22 09:01:43,794 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:01:43,795 - __main__ - INFO - All cache files found on disk
2025-07-22 09:01:43,796 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:01:43,803 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:01:43,810 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:01:44,468 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:01:44,468 - __main__ - INFO - Cache loaded from disk in 0.67s
2025-07-22 09:01:44,468 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:01:44,762 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:01:44,763 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:01:44,763 - __main__ - INFO - Starting system monitor...
2025-07-22 09:01:44,765 - __main__ - INFO - System monitor started
2025-07-22 09:02:12,055 - __main__ - INFO - Request 1753189332-1913562778656: GET http://localhost:8000/search?q=car&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:02:12,056 - __main__ - INFO - Processing search request: 'car' on table 'ReportNLP'
2025-07-22 09:02:12,057 - __main__ - INFO - Processing search: 'car' (Memory: 478.7MB)
2025-07-22 09:02:12,922 - __main__ - INFO - Search completed: 20 results in 863.88ms (Memory: 478.7MB -> 636.4MB, diff: +157.7MB)
2025-07-22 09:02:13,100 - __main__ - WARNING - Request 1753189332-1913562778656 increased memory by 157.8MB (from 478.6MB to 636.5MB)
2025-07-22 09:02:13,266 - __main__ - INFO - Request 1753189332-1913562778656 completed in 1045.29ms with status 200
2025-07-22 09:03:01,019 - __main__ - INFO - Request 1753189381-1913562792928: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:03:01,020 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-22 09:03:01,020 - __main__ - INFO - Processing search: 'theft' (Memory: 636.5MB)
2025-07-22 09:03:01,708 - __main__ - INFO - Search completed: 20 results in 688.13ms (Memory: 636.5MB -> 636.6MB, diff: +0.2MB)
2025-07-22 09:03:01,710 - __main__ - INFO - Request 1753189381-1913562792928 completed in 691.13ms with status 200
2025-07-22 09:05:03,643 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:05:04,649 - __main__ - INFO - System monitor stopped
2025-07-22 09:05:04,652 - __main__ - INFO - Database connection closed
2025-07-22 09:09:00,836 - __main__ - INFO - Starting up Search API...
2025-07-22 09:09:00,837 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:09:00,843 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:09:00,844 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:09:03,522 - __main__ - INFO - Model loaded successfully
2025-07-22 09:09:03,523 - __main__ - INFO - Initializing database connection...
2025-07-22 09:09:03,659 - __main__ - INFO - Database connection established
2025-07-22 09:09:03,659 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:09:03,661 - __main__ - INFO - All cache files found on disk
2025-07-22 09:09:03,661 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:09:03,680 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:09:03,696 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:09:04,413 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:09:04,413 - __main__ - INFO - Cache loaded from disk in 0.75s
2025-07-22 09:09:04,414 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:09:04,734 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:09:04,735 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:09:04,735 - __main__ - INFO - Starting system monitor...
2025-07-22 09:09:04,736 - __main__ - INFO - System monitor started
2025-07-22 09:09:22,925 - __main__ - INFO - Request 1753189762-2385650666864: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:09:22,927 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-22 09:09:22,927 - __main__ - INFO - Processing search: 'theft' (Memory: 477.8MB)
2025-07-22 09:09:23,712 - __main__ - INFO - Search completed: 20 results in 785.05ms (Memory: 477.8MB -> 636.1MB, diff: +158.3MB)
2025-07-22 09:09:23,872 - __main__ - WARNING - Request 1753189762-2385650666864 increased memory by 158.4MB (from 477.7MB to 636.2MB)
2025-07-22 09:09:24,041 - __main__ - INFO - Request 1753189762-2385650666864 completed in 946.36ms with status 200
2025-07-22 09:10:00,191 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:10:01,199 - __main__ - INFO - System monitor stopped
2025-07-22 09:10:01,201 - __main__ - INFO - Database connection closed
2025-07-22 09:13:06,735 - __main__ - INFO - Starting up Search API...
2025-07-22 09:13:06,735 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:13:06,739 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:13:06,739 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:13:10,013 - __main__ - INFO - Model loaded successfully
2025-07-22 09:13:10,013 - __main__ - INFO - Initializing database connection...
2025-07-22 09:13:10,115 - __main__ - INFO - Database connection established
2025-07-22 09:13:10,115 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:13:10,116 - __main__ - INFO - All cache files found on disk
2025-07-22 09:13:10,117 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:13:10,124 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:13:10,131 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:13:10,679 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:13:10,680 - __main__ - INFO - Cache loaded from disk in 0.56s
2025-07-22 09:13:10,680 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:13:11,029 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:13:11,030 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:13:11,030 - __main__ - INFO - Starting system monitor...
2025-07-22 09:13:11,031 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 09:13:40,269 - __main__ - INFO - Request 1753190020-3123898918544: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:13:40,270 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-22 09:13:40,270 - __main__ - INFO - Processing search: 'theft' (Memory: 478.2MB)
2025-07-22 09:13:41,072 - __main__ - INFO - Search completed: 20 results in 802.06ms (Memory: 478.2MB -> 636.9MB, diff: +158.6MB)
2025-07-22 09:13:41,228 - __main__ - WARNING - Request 1753190020-3123898918544 increased memory by 158.8MB (from 478.2MB to 637.0MB)
2025-07-22 09:13:41,366 - __main__ - INFO - Request 1753190020-3123898918544 completed in 958.87ms with status 200
2025-07-22 09:14:14,552 - __main__ - INFO - Request 1753190054-3123898898272: GET http://localhost:8000/search?q=fight&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:14:14,553 - __main__ - INFO - Processing search request: 'fight' on table 'ReportNLP'
2025-07-22 09:14:14,553 - __main__ - INFO - Processing search: 'fight' (Memory: 637.0MB)
2025-07-22 09:14:15,223 - __main__ - INFO - Search completed: 20 results in 669.20ms (Memory: 637.0MB -> 638.1MB, diff: +1.1MB)
2025-07-22 09:14:15,226 - __main__ - INFO - Request 1753190054-3123898898272 completed in 673.20ms with status 200
2025-07-22 09:16:08,788 - __main__ - INFO - Request 1753190168-3123916308304: GET http://localhost:8000/memory
2025-07-22 09:16:08,788 - __main__ - INFO - Request 1753190168-3123916308304 completed in 1.60ms with status 200
2025-07-22 09:17:55,015 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:17:56,018 - __main__ - INFO - System monitor stopped
2025-07-22 09:17:56,021 - __main__ - INFO - Database connection closed
2025-07-22 09:19:22,596 - __main__ - INFO - Starting up Search API...
2025-07-22 09:19:22,597 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:19:22,600 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:19:22,601 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:19:25,996 - __main__ - INFO - Model loaded successfully
2025-07-22 09:19:25,997 - __main__ - INFO - Initializing database connection...
2025-07-22 09:19:26,106 - __main__ - INFO - Database connection established
2025-07-22 09:19:26,107 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:19:26,108 - __main__ - INFO - All cache files found on disk
2025-07-22 09:19:26,109 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:19:26,117 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:19:26,127 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:19:26,827 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:19:26,828 - __main__ - INFO - Cache loaded from disk in 0.72s
2025-07-22 09:19:26,828 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:19:27,224 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:19:27,225 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:19:27,225 - __main__ - INFO - Starting system monitor...
2025-07-22 09:19:27,227 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 09:20:07,299 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:20:08,301 - __main__ - INFO - System monitor stopped
2025-07-22 09:20:08,309 - __main__ - INFO - Database connection closed
2025-07-22 09:20:53,733 - __main__ - INFO - Starting up Search API...
2025-07-22 09:20:53,733 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:20:53,737 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:20:53,737 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:20:56,777 - __main__ - INFO - Model loaded successfully
2025-07-22 09:20:56,778 - __main__ - INFO - Initializing database connection...
2025-07-22 09:20:56,911 - __main__ - INFO - Database connection established
2025-07-22 09:20:56,912 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:20:56,914 - __main__ - INFO - All cache files found on disk
2025-07-22 09:20:56,914 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:20:56,922 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:20:56,930 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:20:57,528 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:20:57,528 - __main__ - INFO - Cache loaded from disk in 0.62s
2025-07-22 09:20:57,528 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:20:57,850 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:20:57,851 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:20:57,851 - __main__ - INFO - Starting system monitor...
2025-07-22 09:20:57,853 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 09:21:52,785 - __main__ - INFO - Request 1753190512-2901891251568: GET http://localhost:8000/docs
2025-07-22 09:21:52,786 - __main__ - INFO - Request 1753190512-2901891251568 completed in 2.00ms with status 200
2025-07-22 09:21:53,404 - __main__ - INFO - Request 1753190513-2901928406016: GET http://localhost:8000/openapi.json
2025-07-22 09:21:53,425 - __main__ - INFO - Request 1753190513-2901928406016 completed in 21.70ms with status 200
2025-07-22 09:22:08,352 - __main__ - INFO - Request 1753190528-2901931905184: GET http://localhost:8000/monitor
2025-07-22 09:22:08,353 - __main__ - INFO - Request 1753190528-2901931905184 completed in 1.01ms with status 200
2025-07-22 09:23:51,575 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:23:52,582 - __main__ - INFO - System monitor stopped
2025-07-22 09:23:52,585 - __main__ - INFO - Database connection closed
2025-07-22 10:25:10,899 - __main__ - INFO - Starting up Search API...
2025-07-22 10:25:10,900 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 10:25:10,907 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 10:25:10,907 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 10:25:14,111 - __main__ - INFO - Model loaded successfully
2025-07-22 10:25:14,111 - __main__ - INFO - Initializing database connection...
2025-07-22 10:25:14,259 - __main__ - INFO - Database connection established
2025-07-22 10:25:14,259 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 10:25:14,261 - __main__ - INFO - All cache files found on disk
2025-07-22 10:25:14,261 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 10:25:14,287 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 10:25:14,307 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 10:25:14,990 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 10:25:14,990 - __main__ - INFO - Cache loaded from disk in 0.73s
2025-07-22 10:25:14,991 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 10:25:15,277 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 10:25:15,278 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 10:25:15,278 - __main__ - INFO - Starting system monitor...
2025-07-22 10:25:15,280 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 10:25:15,280 - __main__ - INFO - Starting cache update monitor...
2025-07-22 10:25:15,280 - __main__ - INFO - Cache update monitor started
2025-07-22 10:25:15,280 - __main__ - INFO - Cache update monitor started
2025-07-22 10:25:38,386 - __main__ - INFO - Found 317785 new records since 2011-01-05 14:42:58.903000
2025-07-22 10:25:38,389 - __main__ - INFO - Starting incremental update for 317785 new records
2025-07-22 10:25:38,389 - __main__ - INFO - Loading new records since 2011-01-05 14:42:58.903000 with chunk size 5000
2025-07-22 10:25:38,771 - __main__ - INFO - Loading 317785 new records incrementally
2025-07-22 10:25:38,772 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-22 10:25:38,833 - __main__ - ERROR - Failed to load new records incrementally: Token error: 'Conversion failed when converting date and/or time from character string.' on server HQDCSMOSQL01 executing  on line 1 (code: 241, state: 1, class: 16)
2025-07-22 10:25:38,833 - __main__ - ERROR - Incremental cache update process failed: Incremental loading failed: Token error: 'Conversion failed when converting date and/or time from character string.' on server HQDCSMOSQL01 executing  on line 1 (code: 241, state: 1, class: 16)
2025-07-22 10:25:38,835 - __main__ - WARNING - Automatic cache update failed: {'success': False, 'reason': 'Update process failed', 'error': "Incremental loading failed: Token error: 'Conversion failed when converting date and/or time from character string.' on server HQDCSMOSQL01 executing  on line 1 (code: 241, state: 1, class: 16)"}
2025-07-22 10:26:24,062 - __main__ - INFO - Shutting down Search API...
2025-07-22 10:26:26,066 - __main__ - INFO - Cache update monitor stopped
2025-07-22 10:26:27,069 - __main__ - INFO - System monitor stopped
2025-07-22 10:26:27,071 - __main__ - INFO - Database connection closed
2025-07-22 10:28:12,777 - __main__ - INFO - Starting up Search API...
2025-07-22 10:28:12,778 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 10:28:12,781 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 10:28:12,781 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 10:28:15,751 - __main__ - INFO - Model loaded successfully
2025-07-22 10:28:15,751 - __main__ - INFO - Initializing database connection...
2025-07-22 10:28:15,870 - __main__ - INFO - Database connection established
2025-07-22 10:28:15,871 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 10:28:15,872 - __main__ - INFO - All cache files found on disk
2025-07-22 10:28:15,872 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 10:28:15,882 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 10:28:15,890 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 10:28:16,496 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 10:28:16,497 - __main__ - INFO - Cache loaded from disk in 0.63s
2025-07-22 10:28:16,497 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 10:28:16,829 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 10:28:16,829 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 10:28:16,830 - __main__ - INFO - Starting system monitor...
2025-07-22 10:28:16,832 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 10:28:16,832 - __main__ - INFO - Starting cache update monitor...
2025-07-22 10:28:16,833 - __main__ - INFO - Cache update monitor started
2025-07-22 10:28:16,833 - __main__ - INFO - Cache update monitor started
2025-07-22 10:28:37,981 - __main__ - INFO - Found 317785 new records since 2011-01-05 14:42:58.903000
2025-07-22 10:28:37,983 - __main__ - INFO - Starting incremental update for 317785 new records
2025-07-22 10:28:37,984 - __main__ - INFO - Loading new records since 2011-01-05 14:42:58.903000 with chunk size 5000
2025-07-22 10:28:38,354 - __main__ - INFO - Loading 317785 new records incrementally
2025-07-22 10:28:38,355 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-22 10:28:42,335 - __main__ - INFO - Incremental progress: 5000/317785 records processed (1.6%)
2025-07-22 10:28:42,336 - __main__ - INFO - Processing incremental chunk 2: records 5000-10000
2025-07-22 10:28:46,418 - __main__ - INFO - Incremental progress: 10000/317785 records processed (3.1%)
2025-07-22 10:28:46,419 - __main__ - INFO - Processing incremental chunk 3: records 10000-15000
2025-07-22 10:28:51,777 - __main__ - INFO - Incremental progress: 15000/317785 records processed (4.7%)
2025-07-22 10:28:51,777 - __main__ - INFO - Processing incremental chunk 4: records 15000-20000
2025-07-22 10:28:55,939 - __main__ - INFO - Incremental progress: 20000/317785 records processed (6.3%)
2025-07-22 10:28:55,939 - __main__ - INFO - Processing incremental chunk 5: records 20000-25000
2025-07-22 10:29:00,114 - __main__ - INFO - Incremental progress: 25000/317785 records processed (7.9%)
2025-07-22 10:29:00,115 - __main__ - INFO - Processing incremental chunk 6: records 25000-30000
2025-07-22 10:29:04,203 - __main__ - INFO - Incremental progress: 30000/317785 records processed (9.4%)
2025-07-22 10:29:04,204 - __main__ - INFO - Processing incremental chunk 7: records 30000-35000
2025-07-22 10:29:08,602 - __main__ - INFO - Incremental progress: 35000/317785 records processed (11.0%)
2025-07-22 10:29:08,603 - __main__ - INFO - Processing incremental chunk 8: records 35000-40000
2025-07-22 10:29:13,014 - __main__ - INFO - Incremental progress: 40000/317785 records processed (12.6%)
2025-07-22 10:29:13,015 - __main__ - INFO - Processing incremental chunk 9: records 40000-45000
2025-07-22 10:29:17,658 - __main__ - INFO - Incremental progress: 45000/317785 records processed (14.2%)
2025-07-22 10:29:17,658 - __main__ - INFO - Processing incremental chunk 10: records 45000-50000
2025-07-22 10:29:22,427 - __main__ - INFO - Incremental progress: 50000/317785 records processed (15.7%)
2025-07-22 10:29:22,427 - __main__ - INFO - Processing incremental chunk 11: records 50000-55000
2025-07-22 10:29:27,298 - __main__ - INFO - Request 1753194567-1710556287664: GET http://localhost:8000/search?q=fight&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 10:29:27,300 - __main__ - INFO - Processing search request: 'fight' on table 'ReportNLP'
2025-07-22 10:29:27,301 - __main__ - INFO - Processing search: 'fight' (Memory: 716.9MB)
2025-07-22 10:29:28,166 - __main__ - INFO - Incremental progress: 55000/317785 records processed (17.3%)
2025-07-22 10:29:28,167 - __main__ - INFO - Processing incremental chunk 12: records 55000-60000
2025-07-22 10:29:29,538 - __main__ - INFO - Search completed: 20 results in 2238.57ms (Memory: 716.9MB -> 874.3MB, diff: +157.5MB)
2025-07-22 10:29:29,737 - __main__ - WARNING - Request 1753194567-1710556287664 increased memory by 157.7MB (from 716.8MB to 874.5MB)
2025-07-22 10:29:29,909 - __main__ - INFO - Request 1753194567-1710556287664 completed in 2439.58ms with status 200
2025-07-22 10:29:33,979 - __main__ - INFO - Incremental progress: 60000/317785 records processed (18.9%)
2025-07-22 10:29:33,979 - __main__ - INFO - Processing incremental chunk 13: records 60000-65000
2025-07-22 10:29:39,224 - __main__ - INFO - Incremental progress: 65000/317785 records processed (20.5%)
2025-07-22 10:29:39,225 - __main__ - INFO - Processing incremental chunk 14: records 65000-70000
2025-07-22 10:29:44,488 - __main__ - INFO - Incremental progress: 70000/317785 records processed (22.0%)
2025-07-22 10:29:44,488 - __main__ - INFO - Processing incremental chunk 15: records 70000-75000
2025-07-22 10:29:50,204 - __main__ - INFO - Incremental progress: 75000/317785 records processed (23.6%)
2025-07-22 10:29:50,205 - __main__ - INFO - Processing incremental chunk 16: records 75000-80000
2025-07-22 10:29:55,859 - __main__ - INFO - Incremental progress: 80000/317785 records processed (25.2%)
2025-07-22 10:29:55,860 - __main__ - INFO - Processing incremental chunk 17: records 80000-85000
2025-07-22 10:30:01,161 - __main__ - INFO - Incremental progress: 85000/317785 records processed (26.7%)
2025-07-22 10:30:01,161 - __main__ - INFO - Processing incremental chunk 18: records 85000-90000
2025-07-22 10:30:06,532 - __main__ - INFO - Incremental progress: 90000/317785 records processed (28.3%)
2025-07-22 10:30:06,532 - __main__ - INFO - Processing incremental chunk 19: records 90000-95000
2025-07-22 10:30:11,952 - __main__ - INFO - Incremental progress: 95000/317785 records processed (29.9%)
2025-07-22 10:30:11,953 - __main__ - INFO - Processing incremental chunk 20: records 95000-100000
2025-07-22 10:30:17,424 - __main__ - INFO - Incremental progress: 100000/317785 records processed (31.5%)
2025-07-22 10:30:17,425 - __main__ - INFO - Processing incremental chunk 21: records 100000-105000
2025-07-22 10:30:23,300 - __main__ - INFO - Incremental progress: 105000/317785 records processed (33.0%)
2025-07-22 10:30:23,301 - __main__ - INFO - Processing incremental chunk 22: records 105000-110000
2025-07-22 10:30:29,284 - __main__ - INFO - Incremental progress: 110000/317785 records processed (34.6%)
2025-07-22 10:30:29,284 - __main__ - INFO - Processing incremental chunk 23: records 110000-115000
2025-07-22 10:30:34,754 - __main__ - INFO - Incremental progress: 115000/317785 records processed (36.2%)
2025-07-22 10:30:34,755 - __main__ - INFO - Processing incremental chunk 24: records 115000-120000
2025-07-22 10:30:40,305 - __main__ - INFO - Incremental progress: 120000/317785 records processed (37.8%)
2025-07-22 10:30:40,306 - __main__ - INFO - Processing incremental chunk 25: records 120000-125000
2025-07-22 10:30:46,482 - __main__ - INFO - Incremental progress: 125000/317785 records processed (39.3%)
2025-07-22 10:30:46,482 - __main__ - INFO - Processing incremental chunk 26: records 125000-130000
2025-07-22 10:30:53,062 - __main__ - INFO - Incremental progress: 130000/317785 records processed (40.9%)
2025-07-22 10:30:53,062 - __main__ - INFO - Processing incremental chunk 27: records 130000-135000
2025-07-22 10:30:59,926 - __main__ - INFO - Incremental progress: 135000/317785 records processed (42.5%)
2025-07-22 10:30:59,926 - __main__ - INFO - Processing incremental chunk 28: records 135000-140000
2025-07-22 10:31:06,458 - __main__ - INFO - Incremental progress: 140000/317785 records processed (44.1%)
2025-07-22 10:31:06,458 - __main__ - INFO - Processing incremental chunk 29: records 140000-145000
2025-07-22 10:31:13,222 - __main__ - INFO - Incremental progress: 145000/317785 records processed (45.6%)
2025-07-22 10:31:13,222 - __main__ - INFO - Processing incremental chunk 30: records 145000-150000
2025-07-22 10:31:20,269 - __main__ - INFO - Incremental progress: 150000/317785 records processed (47.2%)
2025-07-22 10:31:20,269 - __main__ - INFO - Processing incremental chunk 31: records 150000-155000
2025-07-22 10:31:27,517 - __main__ - INFO - Incremental progress: 155000/317785 records processed (48.8%)
2025-07-22 10:31:27,518 - __main__ - INFO - Processing incremental chunk 32: records 155000-160000
2025-07-22 10:31:36,694 - __main__ - INFO - Incremental progress: 160000/317785 records processed (50.3%)
2025-07-22 10:31:36,695 - __main__ - INFO - Processing incremental chunk 33: records 160000-165000
2025-07-22 10:31:45,983 - __main__ - INFO - Incremental progress: 165000/317785 records processed (51.9%)
2025-07-22 10:31:45,985 - __main__ - INFO - Processing incremental chunk 34: records 165000-170000
2025-07-22 10:31:55,259 - __main__ - INFO - Incremental progress: 170000/317785 records processed (53.5%)
2025-07-22 10:31:55,259 - __main__ - INFO - Processing incremental chunk 35: records 170000-175000
2025-07-22 10:32:04,958 - __main__ - INFO - Incremental progress: 175000/317785 records processed (55.1%)
2025-07-22 10:32:04,958 - __main__ - INFO - Processing incremental chunk 36: records 175000-180000
2025-07-22 10:32:14,311 - __main__ - INFO - Incremental progress: 180000/317785 records processed (56.6%)
2025-07-22 10:32:14,312 - __main__ - INFO - Processing incremental chunk 37: records 180000-185000
2025-07-22 10:32:22,530 - __main__ - INFO - Incremental progress: 185000/317785 records processed (58.2%)
2025-07-22 10:32:22,531 - __main__ - INFO - Processing incremental chunk 38: records 185000-190000
2025-07-22 10:32:31,008 - __main__ - INFO - Incremental progress: 190000/317785 records processed (59.8%)
2025-07-22 10:32:31,008 - __main__ - INFO - Processing incremental chunk 39: records 190000-195000
2025-07-22 10:32:39,534 - __main__ - INFO - Incremental progress: 195000/317785 records processed (61.4%)
2025-07-22 10:32:39,535 - __main__ - INFO - Processing incremental chunk 40: records 195000-200000
2025-07-22 10:32:48,818 - __main__ - INFO - Incremental progress: 200000/317785 records processed (62.9%)
2025-07-22 10:32:48,818 - __main__ - INFO - Processing incremental chunk 41: records 200000-205000
2025-07-22 10:32:57,873 - __main__ - INFO - Incremental progress: 205000/317785 records processed (64.5%)
2025-07-22 10:32:57,873 - __main__ - INFO - Processing incremental chunk 42: records 205000-210000
2025-07-22 10:33:06,489 - __main__ - INFO - Incremental progress: 210000/317785 records processed (66.1%)
2025-07-22 10:33:06,489 - __main__ - INFO - Processing incremental chunk 43: records 210000-215000
2025-07-22 10:33:14,659 - __main__ - INFO - Incremental progress: 215000/317785 records processed (67.7%)
2025-07-22 10:33:14,660 - __main__ - INFO - Processing incremental chunk 44: records 215000-220000
2025-07-22 10:33:22,397 - __main__ - INFO - Incremental progress: 220000/317785 records processed (69.2%)
2025-07-22 10:33:22,397 - __main__ - INFO - Processing incremental chunk 45: records 220000-225000
2025-07-22 10:33:30,887 - __main__ - INFO - Incremental progress: 225000/317785 records processed (70.8%)
2025-07-22 10:33:30,888 - __main__ - INFO - Processing incremental chunk 46: records 225000-230000
2025-07-22 10:33:39,363 - __main__ - INFO - Incremental progress: 230000/317785 records processed (72.4%)
2025-07-22 10:33:39,363 - __main__ - INFO - Processing incremental chunk 47: records 230000-235000
2025-07-22 10:33:51,613 - __main__ - INFO - Incremental progress: 235000/317785 records processed (73.9%)
2025-07-22 10:33:51,614 - __main__ - INFO - Processing incremental chunk 48: records 235000-240000
2025-07-22 10:34:01,121 - __main__ - INFO - Incremental progress: 240000/317785 records processed (75.5%)
2025-07-22 10:34:01,122 - __main__ - INFO - Processing incremental chunk 49: records 240000-245000
2025-07-22 10:34:09,923 - __main__ - INFO - Incremental progress: 245000/317785 records processed (77.1%)
2025-07-22 10:34:09,924 - __main__ - INFO - Processing incremental chunk 50: records 245000-250000
2025-07-22 10:34:18,935 - __main__ - INFO - Incremental progress: 250000/317785 records processed (78.7%)
2025-07-22 10:34:18,936 - __main__ - INFO - Processing incremental chunk 51: records 250000-255000
2025-07-22 10:34:27,339 - __main__ - INFO - Incremental progress: 255000/317785 records processed (80.2%)
2025-07-22 10:34:27,339 - __main__ - INFO - Processing incremental chunk 52: records 255000-260000
2025-07-22 10:34:36,283 - __main__ - INFO - Incremental progress: 260000/317785 records processed (81.8%)
2025-07-22 10:34:36,283 - __main__ - INFO - Processing incremental chunk 53: records 260000-265000
2025-07-22 10:34:45,758 - __main__ - INFO - Incremental progress: 265000/317785 records processed (83.4%)
2025-07-22 10:34:45,759 - __main__ - INFO - Processing incremental chunk 54: records 265000-270000
2025-07-22 10:34:55,927 - __main__ - INFO - Incremental progress: 270000/317785 records processed (85.0%)
2025-07-22 10:34:55,927 - __main__ - INFO - Processing incremental chunk 55: records 270000-275000
2025-07-22 10:35:05,300 - __main__ - INFO - Incremental progress: 275000/317785 records processed (86.5%)
2025-07-22 10:35:05,301 - __main__ - INFO - Processing incremental chunk 56: records 275000-280000
2025-07-22 10:35:14,856 - __main__ - INFO - Incremental progress: 280000/317785 records processed (88.1%)
2025-07-22 10:35:14,857 - __main__ - INFO - Processing incremental chunk 57: records 280000-285000
2025-07-22 10:35:24,008 - __main__ - INFO - Incremental progress: 285000/317785 records processed (89.7%)
2025-07-22 10:35:24,008 - __main__ - INFO - Processing incremental chunk 58: records 285000-290000
2025-07-22 10:35:33,191 - __main__ - INFO - Incremental progress: 290000/317785 records processed (91.3%)
2025-07-22 10:35:33,192 - __main__ - INFO - Processing incremental chunk 59: records 290000-295000
2025-07-22 10:35:42,189 - __main__ - INFO - Incremental progress: 295000/317785 records processed (92.8%)
2025-07-22 10:35:42,189 - __main__ - INFO - Processing incremental chunk 60: records 295000-300000
2025-07-22 10:35:52,166 - __main__ - INFO - Incremental progress: 300000/317785 records processed (94.4%)
2025-07-22 10:35:52,167 - __main__ - INFO - Processing incremental chunk 61: records 300000-305000
2025-07-22 10:36:01,286 - __main__ - INFO - Incremental progress: 305000/317785 records processed (96.0%)
2025-07-22 10:36:01,286 - __main__ - INFO - Processing incremental chunk 62: records 305000-310000
2025-07-22 10:36:10,926 - __main__ - INFO - Incremental progress: 310000/317785 records processed (97.6%)
2025-07-22 10:36:10,927 - __main__ - INFO - Processing incremental chunk 63: records 310000-315000
2025-07-22 10:36:20,406 - __main__ - INFO - Incremental progress: 315000/317785 records processed (99.1%)
2025-07-22 10:36:20,407 - __main__ - INFO - Processing incremental chunk 64: records 315000-317785
2025-07-22 10:36:29,314 - __main__ - INFO - Incremental progress: 317785/317785 records processed (100.0%)
2025-07-22 10:36:29,314 - __main__ - INFO - Incremental loading complete: 317785 new records loaded
2025-07-22 10:36:29,316 - __main__ - WARNING - New records would use 465.5MB, exceeding limit of 100MB
2025-07-22 10:36:29,666 - __main__ - WARNING - Automatic cache update failed: {'success': False, 'reason': 'Memory limit exceeded', 'estimated_memory_mb': 465.50537109375, 'limit_mb': 100}
2025-07-22 10:38:55,474 - __main__ - INFO - Request 1753195135-1710556283824: GET http://localhost:8000/search?q=fight&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 10:38:55,475 - __main__ - INFO - Processing search request: 'fight' on table 'ReportNLP'
2025-07-22 10:38:55,475 - __main__ - INFO - Processing search: 'fight' (Memory: 746.6MB)
2025-07-22 10:38:56,235 - __main__ - INFO - Search completed: 20 results in 760.41ms (Memory: 746.6MB -> 747.9MB, diff: +1.3MB)
2025-07-22 10:38:56,240 - __main__ - INFO - Request 1753195135-1710556283824 completed in 767.41ms with status 200
2025-07-22 10:39:05,795 - __main__ - INFO - Shutting down Search API...
2025-07-22 10:39:07,796 - __main__ - INFO - Cache update monitor stopped
2025-07-22 10:39:08,802 - __main__ - INFO - System monitor stopped
2025-07-22 10:39:08,805 - __main__ - INFO - Database connection closed
2025-07-22 10:43:39,954 - __main__ - INFO - Starting up Search API...
2025-07-22 10:43:39,955 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 10:43:39,960 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 10:43:39,960 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 10:43:43,232 - __main__ - INFO - Model loaded successfully
2025-07-22 10:43:43,233 - __main__ - INFO - Initializing database connection...
2025-07-22 10:43:43,380 - __main__ - INFO - Database connection established
2025-07-22 10:43:43,380 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 10:43:43,381 - __main__ - INFO - All cache files found on disk
2025-07-22 10:43:43,382 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 10:43:43,403 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 10:43:43,422 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 10:43:44,115 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 10:43:44,116 - __main__ - INFO - Cache loaded from disk in 0.73s
2025-07-22 10:43:44,116 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 10:43:44,446 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 10:43:44,446 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 10:43:44,447 - __main__ - INFO - Starting system monitor...
2025-07-22 10:43:44,448 - __main__ - INFO - System monitor started with persistent bottom status bar
2025-07-22 10:43:44,448 - __main__ - INFO - Starting cache update monitor...
2025-07-22 10:43:44,449 - __main__ - INFO - Cache update monitor started
2025-07-22 10:43:44,449 - __main__ - INFO - Cache update monitor started
2025-07-22 10:44:04,409 - __main__ - INFO - Found 317785 new records since 2011-01-05 14:42:58.903000
2025-07-22 10:44:04,412 - __main__ - INFO - Starting incremental update for 317785 new records
2025-07-22 10:44:04,412 - __main__ - INFO - Loading new records since 2011-01-05 14:42:58.903000 with chunk size 5000
2025-07-22 10:44:04,788 - __main__ - INFO - Loading 317785 new records incrementally
2025-07-22 10:44:04,789 - __main__ - INFO - Processing incremental chunk 1: records 0-5000
2025-07-22 10:44:08,260 - __main__ - INFO - Incremental progress: 5000/317785 records processed (1.6%)
2025-07-22 10:44:08,260 - __main__ - INFO - Processing incremental chunk 2: records 5000-10000
2025-07-22 10:44:11,955 - __main__ - INFO - Incremental progress: 10000/317785 records processed (3.1%)
2025-07-22 10:44:11,955 - __main__ - INFO - Processing incremental chunk 3: records 10000-15000
2025-07-22 10:44:16,117 - __main__ - INFO - Incremental progress: 15000/317785 records processed (4.7%)
2025-07-22 10:44:16,118 - __main__ - INFO - Processing incremental chunk 4: records 15000-20000
2025-07-22 10:44:20,264 - __main__ - INFO - Incremental progress: 20000/317785 records processed (6.3%)
2025-07-22 10:44:20,265 - __main__ - INFO - Processing incremental chunk 5: records 20000-25000
2025-07-22 10:44:24,505 - __main__ - INFO - Incremental progress: 25000/317785 records processed (7.9%)
2025-07-22 10:44:24,506 - __main__ - INFO - Processing incremental chunk 6: records 25000-30000
2025-07-22 10:44:28,755 - __main__ - INFO - Incremental progress: 30000/317785 records processed (9.4%)
2025-07-22 10:44:28,756 - __main__ - INFO - Processing incremental chunk 7: records 30000-35000
2025-07-22 10:44:33,339 - __main__ - INFO - Incremental progress: 35000/317785 records processed (11.0%)
2025-07-22 10:44:33,340 - __main__ - INFO - Processing incremental chunk 8: records 35000-40000
2025-07-22 10:44:37,657 - __main__ - INFO - Incremental progress: 40000/317785 records processed (12.6%)
2025-07-22 10:44:37,658 - __main__ - INFO - Processing incremental chunk 9: records 40000-45000
2025-07-22 10:44:42,341 - __main__ - INFO - Incremental progress: 45000/317785 records processed (14.2%)
2025-07-22 10:44:42,342 - __main__ - INFO - Processing incremental chunk 10: records 45000-50000
2025-07-22 10:44:47,079 - __main__ - INFO - Incremental progress: 50000/317785 records processed (15.7%)
2025-07-22 10:44:47,080 - __main__ - INFO - Processing incremental chunk 11: records 50000-55000
2025-07-22 10:44:52,446 - __main__ - INFO - Incremental progress: 55000/317785 records processed (17.3%)
2025-07-22 10:44:52,447 - __main__ - INFO - Processing incremental chunk 12: records 55000-60000
2025-07-22 10:44:58,041 - __main__ - INFO - Incremental progress: 60000/317785 records processed (18.9%)
2025-07-22 10:44:58,042 - __main__ - INFO - Processing incremental chunk 13: records 60000-65000
2025-07-22 10:45:03,229 - __main__ - INFO - Incremental progress: 65000/317785 records processed (20.5%)
2025-07-22 10:45:03,229 - __main__ - INFO - Processing incremental chunk 14: records 65000-70000
2025-07-22 10:45:08,088 - __main__ - INFO - Incremental progress: 70000/317785 records processed (22.0%)
2025-07-22 10:45:08,089 - __main__ - INFO - Processing incremental chunk 15: records 70000-75000
2025-07-22 10:45:13,870 - __main__ - INFO - Incremental progress: 75000/317785 records processed (23.6%)
2025-07-22 10:45:13,871 - __main__ - INFO - Processing incremental chunk 16: records 75000-80000
2025-07-22 10:45:19,127 - __main__ - INFO - Incremental progress: 80000/317785 records processed (25.2%)
2025-07-22 10:45:19,128 - __main__ - INFO - Processing incremental chunk 17: records 80000-85000
2025-07-22 10:45:24,461 - __main__ - INFO - Incremental progress: 85000/317785 records processed (26.7%)
2025-07-22 10:45:24,462 - __main__ - INFO - Processing incremental chunk 18: records 85000-90000
2025-07-22 10:45:29,718 - __main__ - INFO - Incremental progress: 90000/317785 records processed (28.3%)
2025-07-22 10:45:29,719 - __main__ - INFO - Processing incremental chunk 19: records 90000-95000
2025-07-22 10:45:35,507 - __main__ - INFO - Incremental progress: 95000/317785 records processed (29.9%)
2025-07-22 10:45:35,508 - __main__ - INFO - Processing incremental chunk 20: records 95000-100000
2025-07-22 10:45:40,928 - __main__ - INFO - Incremental progress: 100000/317785 records processed (31.5%)
2025-07-22 10:45:40,929 - __main__ - INFO - Processing incremental chunk 21: records 100000-105000
2025-07-22 10:45:46,532 - __main__ - INFO - Incremental progress: 105000/317785 records processed (33.0%)
2025-07-22 10:45:46,532 - __main__ - INFO - Processing incremental chunk 22: records 105000-110000
2025-07-22 10:45:53,091 - __main__ - INFO - Incremental progress: 110000/317785 records processed (34.6%)
2025-07-22 10:45:53,092 - __main__ - INFO - Processing incremental chunk 23: records 110000-115000
2025-07-22 10:45:58,929 - __main__ - INFO - Incremental progress: 115000/317785 records processed (36.2%)
2025-07-22 10:45:58,930 - __main__ - INFO - Processing incremental chunk 24: records 115000-120000
2025-07-22 10:46:04,879 - __main__ - INFO - Incremental progress: 120000/317785 records processed (37.8%)
2025-07-22 10:46:04,880 - __main__ - INFO - Processing incremental chunk 25: records 120000-125000
2025-07-22 10:46:11,607 - __main__ - INFO - Incremental progress: 125000/317785 records processed (39.3%)
2025-07-22 10:46:11,608 - __main__ - INFO - Processing incremental chunk 26: records 125000-130000
2025-07-22 10:46:18,581 - __main__ - INFO - Incremental progress: 130000/317785 records processed (40.9%)
2025-07-22 10:46:18,582 - __main__ - INFO - Processing incremental chunk 27: records 130000-135000
2025-07-22 10:46:25,099 - __main__ - INFO - Incremental progress: 135000/317785 records processed (42.5%)
2025-07-22 10:46:25,100 - __main__ - INFO - Processing incremental chunk 28: records 135000-140000
2025-07-22 10:46:31,710 - __main__ - INFO - Incremental progress: 140000/317785 records processed (44.1%)
2025-07-22 10:46:31,710 - __main__ - INFO - Processing incremental chunk 29: records 140000-145000
2025-07-22 10:46:38,372 - __main__ - INFO - Incremental progress: 145000/317785 records processed (45.6%)
2025-07-22 10:46:38,373 - __main__ - INFO - Processing incremental chunk 30: records 145000-150000
2025-07-22 10:46:44,981 - __main__ - INFO - Incremental progress: 150000/317785 records processed (47.2%)
2025-07-22 10:46:44,981 - __main__ - INFO - Processing incremental chunk 31: records 150000-155000
2025-07-22 10:46:51,738 - __main__ - INFO - Incremental progress: 155000/317785 records processed (48.8%)
2025-07-22 10:46:51,739 - __main__ - INFO - Processing incremental chunk 32: records 155000-160000
2025-07-22 10:46:58,505 - __main__ - INFO - Incremental progress: 160000/317785 records processed (50.3%)
2025-07-22 10:46:58,506 - __main__ - INFO - Processing incremental chunk 33: records 160000-165000
2025-07-22 10:47:05,100 - __main__ - INFO - Incremental progress: 165000/317785 records processed (51.9%)
2025-07-22 10:47:05,101 - __main__ - INFO - Processing incremental chunk 34: records 165000-170000
2025-07-22 10:47:11,599 - __main__ - INFO - Incremental progress: 170000/317785 records processed (53.5%)
2025-07-22 10:47:11,600 - __main__ - INFO - Processing incremental chunk 35: records 170000-175000
2025-07-22 10:47:18,469 - __main__ - INFO - Incremental progress: 175000/317785 records processed (55.1%)
2025-07-22 10:47:18,470 - __main__ - INFO - Processing incremental chunk 36: records 175000-180000
2025-07-22 10:47:25,250 - __main__ - INFO - Incremental progress: 180000/317785 records processed (56.6%)
2025-07-22 10:47:25,251 - __main__ - INFO - Processing incremental chunk 37: records 180000-185000
2025-07-22 10:47:32,515 - __main__ - INFO - Incremental progress: 185000/317785 records processed (58.2%)
2025-07-22 10:47:32,516 - __main__ - INFO - Processing incremental chunk 38: records 185000-190000
2025-07-22 10:47:39,812 - __main__ - INFO - Incremental progress: 190000/317785 records processed (59.8%)
2025-07-22 10:47:39,813 - __main__ - INFO - Processing incremental chunk 39: records 190000-195000
2025-07-22 10:47:48,291 - __main__ - INFO - Incremental progress: 195000/317785 records processed (61.4%)
2025-07-22 10:47:48,292 - __main__ - INFO - Processing incremental chunk 40: records 195000-200000
2025-07-22 10:47:56,924 - __main__ - INFO - Incremental progress: 200000/317785 records processed (62.9%)
2025-07-22 10:47:56,925 - __main__ - INFO - Processing incremental chunk 41: records 200000-205000
2025-07-22 10:48:04,404 - __main__ - INFO - Incremental progress: 205000/317785 records processed (64.5%)
2025-07-22 10:48:04,405 - __main__ - INFO - Processing incremental chunk 42: records 205000-210000
2025-07-22 10:48:12,276 - __main__ - INFO - Incremental progress: 210000/317785 records processed (66.1%)
2025-07-22 10:48:12,277 - __main__ - INFO - Processing incremental chunk 43: records 210000-215000
2025-07-22 10:48:20,316 - __main__ - INFO - Incremental progress: 215000/317785 records processed (67.7%)
2025-07-22 10:48:20,317 - __main__ - INFO - Processing incremental chunk 44: records 215000-220000
2025-07-22 10:48:27,799 - __main__ - INFO - Incremental progress: 220000/317785 records processed (69.2%)
2025-07-22 10:48:27,800 - __main__ - INFO - Processing incremental chunk 45: records 220000-225000
2025-07-22 10:48:35,557 - __main__ - INFO - Incremental progress: 225000/317785 records processed (70.8%)
2025-07-22 10:48:35,557 - __main__ - INFO - Processing incremental chunk 46: records 225000-230000
2025-07-22 10:48:43,497 - __main__ - INFO - Incremental progress: 230000/317785 records processed (72.4%)
2025-07-22 10:48:43,498 - __main__ - INFO - Processing incremental chunk 47: records 230000-235000
2025-07-22 10:48:52,479 - __main__ - INFO - Incremental progress: 235000/317785 records processed (73.9%)
2025-07-22 10:48:52,479 - __main__ - INFO - Processing incremental chunk 48: records 235000-240000
2025-07-22 10:49:01,208 - __main__ - INFO - Incremental progress: 240000/317785 records processed (75.5%)
2025-07-22 10:49:01,209 - __main__ - INFO - Processing incremental chunk 49: records 240000-245000
2025-07-22 10:49:09,304 - __main__ - INFO - Incremental progress: 245000/317785 records processed (77.1%)
2025-07-22 10:49:09,305 - __main__ - INFO - Processing incremental chunk 50: records 245000-250000
2025-07-22 10:49:17,867 - __main__ - INFO - Incremental progress: 250000/317785 records processed (78.7%)
2025-07-22 10:49:17,868 - __main__ - INFO - Processing incremental chunk 51: records 250000-255000
2025-07-22 10:49:25,967 - __main__ - INFO - Incremental progress: 255000/317785 records processed (80.2%)
2025-07-22 10:49:25,968 - __main__ - INFO - Processing incremental chunk 52: records 255000-260000
2025-07-22 10:49:35,042 - __main__ - INFO - Incremental progress: 260000/317785 records processed (81.8%)
2025-07-22 10:49:35,043 - __main__ - INFO - Processing incremental chunk 53: records 260000-265000
2025-07-22 10:49:43,944 - __main__ - INFO - Incremental progress: 265000/317785 records processed (83.4%)
2025-07-22 10:49:43,945 - __main__ - INFO - Processing incremental chunk 54: records 265000-270000
2025-07-22 10:49:53,765 - __main__ - INFO - Incremental progress: 270000/317785 records processed (85.0%)
2025-07-22 10:49:53,766 - __main__ - INFO - Processing incremental chunk 55: records 270000-275000
2025-07-22 10:50:03,153 - __main__ - INFO - Incremental progress: 275000/317785 records processed (86.5%)
2025-07-22 10:50:03,154 - __main__ - INFO - Processing incremental chunk 56: records 275000-280000
