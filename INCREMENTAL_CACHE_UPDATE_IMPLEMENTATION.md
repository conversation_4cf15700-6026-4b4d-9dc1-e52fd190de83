# Incremental Cache Update Implementation

## Overview

This implementation adds hot cache update functionality to the search API using **Option A: Append-Based Updates** with `Entered_Time` for incremental detection. The system can now automatically detect new records in the database and update the search cache with minimal downtime.

## Key Features

### 1. **Incremental Detection Using Entered_Time**
- Uses `Entered_Time` field to detect new records since last cache update
- Stores latest `Entered_Time` in cache metadata for efficient baseline tracking
- Fallback mechanism queries database if metadata is unavailable

### 2. **Hot Cache Updates (10-50ms downtime)**
- Extends memory-mapped embedding files in-place
- Updates BM25 indices incrementally
- Atomic pointer swapping for minimal service interruption
- Maintains same batch size (5000) as full cache builds

### 3. **Background Monitoring**
- Automatic periodic checks for new records (default: 5 minutes)
- Configurable update intervals and memory limits
- Graceful error handling and recovery

### 4. **Memory Efficiency**
- Reuses existing memory-mapped files
- Incremental BM25 index updates
- Configurable memory growth limits
- Automatic garbage collection

## Implementation Details

### Core Functions Added

#### Cache Update Detection
```python
def get_latest_entered_time_from_cache()
def check_for_new_records(since_entered_time)
def load_new_records_incremental(since_entered_time, chunk_size=5000)
```

#### Cache Extension
```python
def extend_embeddings_mmap(cache_dir, new_embeddings)
def update_cache_incrementally(new_ids, new_embeddings, new_texts)
def perform_incremental_cache_update()
```

#### BM25 Incremental Updates
```python
def add_documents_incremental(self, new_ids, new_texts)  # Added to DiskBackedBM25 class
```

#### Background Monitoring
```python
def cache_update_monitor_worker()
def start_cache_update_monitor()
def stop_cache_update_monitor()
```

### Configuration Options

```python
cache_update_config = {
    'auto_update_enabled': True,
    'check_interval_seconds': 300,  # 5 minutes
    'batch_size': 5000,  # Same as full cache build
    'max_memory_growth_mb': 100,
    'update_strategy': 'incremental'
}
```

### New API Endpoints

#### Manual Operations
- `POST /cache/update-incremental` - Trigger manual incremental update
- `GET /cache/check-updates` - Check for available updates
- `POST /refresh-cache` - Full cache refresh (existing, enhanced)

#### Configuration Management
- `GET /cache/update-config` - Get current configuration
- `POST /cache/update-config` - Update configuration settings

#### Status Monitoring
- `GET /cache/status` - Enhanced with incremental update info

## Update Process Flow

### 1. **Detection Phase** (Background)
```
Check latest Entered_Time in cache metadata
↓
Query database for records with Entered_Time > latest
↓
If new records found → proceed to loading
```

### 2. **Loading Phase** (Background)
```
Load new records in chunks (same 5000 batch size)
↓
Process embeddings and validate dimensions
↓
Prepare BM25 text data
```

### 3. **Extension Phase** (Background)
```
Create extended memory-mapped embedding file
↓
Copy existing embeddings + append new embeddings
↓
Update BM25 index incrementally
↓
Prepare updated ID list
```

### 4. **Hot Swap Phase** (10-50ms)
```
Atomic file replacement (embeddings.dat)
↓
Update in-memory cache references
↓
Update metadata with latest Entered_Time
↓
Clean up old references
```

## Cache File Structure

### Enhanced Files
```
search_cache/
├── embeddings.dat          # Memory-mapped embeddings (extended)
├── ids.pkl                 # ID list (updated)
├── metadata.pkl            # NEW: Cache metadata with latest Entered_Time
├── bm25_vocab.pkl          # BM25 vocabulary (updated)
├── bm25_doc_lengths.pkl    # BM25 document lengths (updated)
└── bm25_term_doc_freq.pkl  # BM25 term frequencies (updated)
```

### Metadata Structure
```python
{
    'record_count': 75513,
    'latest_entered_time': datetime(2025, 7, 22, 14, 30, 0),
    'created_at': 1721654400.0,
    'last_incremental_update': 1721654500.0,
    'cache_version': '1.0'
}
```

## Benefits

### Performance
- **Minimal Downtime**: 10-50ms during hot swap vs full rebuild
- **Memory Efficient**: Only processes new records
- **Fast Detection**: Uses cached metadata for baseline

### Reliability
- **Atomic Operations**: Ensures cache consistency
- **Fallback Mechanisms**: Graceful degradation on errors
- **Monitoring**: Full visibility into update process

### Scalability
- **Configurable Limits**: Memory and batch size controls
- **Background Processing**: Non-blocking updates
- **Same Architecture**: Reuses existing chunked processing

## Usage Examples

### Check for Updates
```bash
curl http://localhost:8000/cache/check-updates
```

### Manual Incremental Update
```bash
curl -X POST http://localhost:8000/cache/update-incremental
```

### Configure Auto-Updates
```bash
curl -X POST http://localhost:8000/cache/update-config \
  -H "Content-Type: application/json" \
  -d '{"check_interval_seconds": 180, "max_memory_growth_mb": 200}'
```

## Testing

Use the provided test script:
```bash
python test_incremental_update.py
```

This tests all incremental update functionality including detection, updates, and search verification.

## Monitoring

The system provides comprehensive monitoring through:
- API endpoints for status and configuration
- Detailed logging of update operations
- Memory usage tracking
- Error reporting and recovery

The implementation maintains backward compatibility while adding powerful incremental update capabilities with minimal operational overhead.
